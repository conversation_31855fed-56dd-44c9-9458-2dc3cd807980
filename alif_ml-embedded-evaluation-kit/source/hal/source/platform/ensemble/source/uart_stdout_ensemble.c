/*
 * Copyright (C) 2024 Alif Semiconductor - All Rights Reserved.
 * Use, distribution and modification of this code is permitted under the
 * terms stated in the Alif Semiconductor Software License Agreement
 *
 * You should have received a copy of the Alif Semiconductor Software
 * License Agreement with this file. If not, please write to:
 * <EMAIL>, or visit: https://alifsemi.com/license
 */

/**
 * @file uart_stdout_ensemble.c
 * @brief UART stdout implementation for Alif Ensemble platform
 * 
 * This file provides UartPutc and UartGetc functions that bridge
 * the standard retarget.c interface with the Alif-specific UART
 * tracelib functions for console output.
 */

// Function prototypes (normally from uart_stdout.h)
#ifdef __cplusplus
extern "C" {
#endif

void UartStdOutInit(void);
unsigned char UartPutc(unsigned char my_ch);
unsigned char UartGetc(void);
unsigned int GetLine(char *lp, unsigned int len);

#ifdef __cplusplus
}
#endif

#include "uart_tracelib.h"
#include "board.h"
#include <stdint.h>
#include <stddef.h>  // For NULL definition

/**
 * @brief Initialize UART for stdout operations
 * 
 * This function initializes the board pinmux and UART hardware
 * for console output operations.
 */
void UartStdOutInit(void)
{
    // Initialize board pinmux for UART pins
    BOARD_Pinmux_Init();
    
    // Initialize UART hardware through tracelib
    // Note: uart_init() is called by tracelib_init() in platform_drivers.c
    // so we don't need to call it again here
}

/**
 * @brief Output a character to UART
 * 
 * @param my_ch Character to output
 * @return The character that was output
 */
unsigned char UartPutc(unsigned char my_ch)
{
    char ch = (char)my_ch;
    
    // Use the Alif UART send function to output the character
    uart_send(&ch, 1);
    
    return my_ch;
}

/**
 * @brief Get a character from UART
 * 
 * @return The character received from UART
 */
unsigned char UartGetc(void)
{
    // Use the Alif UART receive function to get a character
    char ch = uart_getchar();
    
    return (unsigned char)ch;
}

/**
 * @brief Get a line of input from UART
 * 
 * @param lp Pointer to buffer to store the line
 * @param len Maximum length of the line
 * @return Number of characters read
 */
unsigned int GetLine(char *lp, unsigned int len)
{
    unsigned int cnt = 0;
    char c;
    
    if (lp == NULL || len == 0) {
        return 0;
    }
    
    do {
        c = (char)UartGetc();
        
        switch (c) {
        case '\r':  // Carriage return
            *lp = '\0';  // Null terminate
            UartPutc('\r');
            UartPutc('\n');
            return cnt;
            
        case '\n':  // Line feed
            *lp = '\0';  // Null terminate
            UartPutc('\n');
            return cnt;
            
        case '\b':  // Backspace
        case 0x7F:  // DEL
            if (cnt > 0) {
                cnt--;
                lp--;
                UartPutc('\b');
                UartPutc(' ');
                UartPutc('\b');
            }
            break;
            
        case 0x1B:  // ESC
            *lp = '\0';
            return 0;
            
        default:
            if (cnt < len - 1) {  // Leave space for null terminator
                *lp = c;
                lp++;
                cnt++;
                UartPutc((unsigned char)c);  // Echo character
            }
            break;
        }
    } while (cnt < len - 1);
    
    *lp = '\0';  // Null terminate
    return cnt;
}
