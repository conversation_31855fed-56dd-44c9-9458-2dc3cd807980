/*
 * Copyright (C) 2024 Alif Semiconductor - All Rights Reserved.
 * Use, distribution and modification of this code is permitted under the
 * terms stated in the Alif Semiconductor Software License Agreement
 *
 * You should have received a copy of the Alif Semiconductor Software
 * License Agreement with this file. If not, please write to:
 * <EMAIL>, or visit: https://alifsemi.com/license
 */

/**
 * @file uart_stdout_ensemble.c
 * @brief UART stdout implementation for Alif Ensemble platform
 * 
 * This file provides UartPutc and UartGetc functions that bridge
 * the standard retarget.c interface with the Alif-specific UART
 * tracelib functions for console output.
 */

// Function prototypes (normally from uart_stdout.h)
#ifdef __cplusplus
extern "C" {
#endif

void UartStdOutInit(void);
unsigned char UartPutc(unsigned char my_ch);
unsigned char UartGetc(void);
// Note: GetLine is already declared and implemented in tracelib.h/tracelib.c

#ifdef __cplusplus
}
#endif

#include "uart_tracelib.h"
#include "board.h"
#include <stdint.h>
#include <stddef.h>  // For NULL definition

/**
 * @brief Initialize UART for stdout operations
 * 
 * This function initializes the board pinmux and UART hardware
 * for console output operations.
 */
void UartStdOutInit(void)
{
    // Initialize board pinmux for UART pins
    BOARD_Pinmux_Init();
    
    // Initialize UART hardware through tracelib
    // Note: uart_init() is called by tracelib_init() in platform_drivers.c
    // so we don't need to call it again here
}

/**
 * @brief Output a character to UART
 * 
 * @param my_ch Character to output
 * @return The character that was output
 */
unsigned char UartPutc(unsigned char my_ch)
{
    char ch = (char)my_ch;
    
    // Use the Alif UART send function to output the character
    uart_send(&ch, 1);
    
    return my_ch;
}

/**
 * @brief Get a character from UART
 * 
 * @return The character received from UART
 */
unsigned char UartGetc(void)
{
    // Use the Alif UART receive function to get a character
    char ch = uart_getchar();
    
    return (unsigned char)ch;
}

// Note: GetLine function is already implemented in tracelib.c
// so we don't need to redefine it here to avoid linker conflicts
